import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  serverTimestamp,
  setDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  writeBatch,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  LocalExperience,
  LocalExperienceCreateData,
  LocalExperienceUpdateData,
  ExperienceSearchFilters,
  ExperienceSearchResult,
  ExperienceAvailability,
} from "./local-experiences.types"

/**
 * Local Experiences service for Firebase operations
 */
export class LocalExperiencesService extends BaseService {
  private static readonly COLLECTION = "localExperiences"
  private static readonly AVAILABILITY_SUBCOLLECTION = "availability"

  constructor() {
    super()
  }

  /**
   * Create a new experience
   */
  static async createExperience(experienceData: LocalExperienceCreateData): Promise<string> {
    try {
      const experienceRef = doc(collection(db, this.COLLECTION))
      const experienceId = experienceRef.id

      const newExperience: LocalExperience = {
        ...experienceData,
        id: experienceId,
        createdAt: serverTimestamp() as Timestamp,
        rating: 0,
        reviewCount: 0,
      }

      await setDoc(experienceRef, newExperience)
      return experienceId
    } catch (error) {
      console.error("Error creating experience:", error)
      throw error
    }
  }

  /**
   * Get experience by ID
   */
  static async getExperience(experienceId: string): Promise<ServiceResponse<LocalExperience>> {
    try {
      const experienceRef = doc(db, this.COLLECTION, experienceId)
      const experienceSnap = await getDoc(experienceRef)

      if (!experienceSnap.exists()) {
        return { success: false, error: "Experience not found" }
      }

      const experience = { id: experienceSnap.id, ...experienceSnap.data() } as LocalExperience
      return { success: true, data: experience }
    } catch (error) {
      console.error("Error getting experience:", error)
      return { success: false, error: "Failed to get experience" }
    }
  }

  /**
   * Search experiences with filters
   */
  static async searchExperiences(
    filters: ExperienceSearchFilters,
    pageSize: number = 10,
    lastDoc?: QueryDocumentSnapshot
  ): Promise<ServiceResponse<ExperienceSearchResult>> {
    try {
      let experienceQuery = query(collection(db, this.COLLECTION), where("isActive", "==", true))

      // Apply filters
      if (filters.location) {
        experienceQuery = query(experienceQuery, where("location.city", "==", filters.location))
      }

      if (filters.categories && filters.categories.length > 0) {
        experienceQuery = query(
          experienceQuery,
          where("categories", "array-contains-any", filters.categories)
        )
      }

      if (filters.priceRange) {
        if (filters.priceRange.min) {
          experienceQuery = query(
            experienceQuery,
            where("pricing.basePrice", ">=", filters.priceRange.min)
          )
        }
        if (filters.priceRange.max) {
          experienceQuery = query(
            experienceQuery,
            where("pricing.basePrice", "<=", filters.priceRange.max)
          )
        }
      }

      // Apply sorting
      switch (filters.sortBy) {
        case "price_low":
          experienceQuery = query(experienceQuery, orderBy("pricing.basePrice", "asc"))
          break
        case "price_high":
          experienceQuery = query(experienceQuery, orderBy("pricing.basePrice", "desc"))
          break
        case "rating":
          experienceQuery = query(experienceQuery, orderBy("rating", "desc"))
          break
        case "newest":
        default:
          experienceQuery = query(experienceQuery, orderBy("createdAt", "desc"))
          break
      }

      // Apply pagination
      if (lastDoc) {
        experienceQuery = query(experienceQuery, startAfter(lastDoc))
      }
      experienceQuery = query(experienceQuery, limit(pageSize + 1)) // +1 to check if there are more

      const querySnapshot = await getDocs(experienceQuery)
      let experiences: LocalExperience[] = []
      const docs = querySnapshot.docs

      // Process results and convert to LocalExperience objects
      const allExperiences: LocalExperience[] = docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as LocalExperience
      )

      // Apply client-side title search filter if provided
      let filteredExperiences = allExperiences
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase()
        filteredExperiences = allExperiences.filter(
          (experience) =>
            experience.title.toLowerCase().includes(searchTerm) ||
            experience.description.toLowerCase().includes(searchTerm) ||
            experience.location.city.toLowerCase().includes(searchTerm) ||
            experience.location.country.toLowerCase().includes(searchTerm)
        )
      }

      // Apply pagination to filtered results
      const startIndex = 0 // For now, simple pagination
      const endIndex = Math.min(startIndex + pageSize, filteredExperiences.length)
      experiences = filteredExperiences.slice(startIndex, endIndex)

      const hasMore = filteredExperiences.length > pageSize
      const total = experiences.length // Note: This is the current page count

      return {
        success: true,
        data: {
          experiences,
          total,
          hasMore,
        },
      }
    } catch (error) {
      console.error("Error searching experiences:", error)
      return { success: false, error: "Failed to search experiences" }
    }
  }

  /**
   * Get all experiences (for admin/management)
   */
  static async getAllExperiences(): Promise<ServiceResponse<LocalExperience[]>> {
    try {
      const experiencesQuery = query(collection(db, this.COLLECTION), orderBy("createdAt", "desc"))
      const querySnapshot = await getDocs(experiencesQuery)

      const experiences: LocalExperience[] = querySnapshot.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as LocalExperience
      )

      return { success: true, data: experiences }
    } catch (error) {
      console.error("Error getting all experiences:", error)
      return { success: false, error: "Failed to get experiences" }
    }
  }

  /**
   * Update experience
   */
  static async updateExperience(
    experienceId: string,
    updateData: LocalExperienceUpdateData
  ): Promise<ServiceResponse<void>> {
    try {
      const experienceRef = doc(db, this.COLLECTION, experienceId)
      await updateDoc(experienceRef, updateData)
      return { success: true }
    } catch (error) {
      console.error("Error updating experience:", error)
      return { success: false, error: "Failed to update experience" }
    }
  }

  /**
   * Delete experience
   */
  static async deleteExperience(experienceId: string): Promise<ServiceResponse<void>> {
    try {
      const experienceRef = doc(db, this.COLLECTION, experienceId)
      await deleteDoc(experienceRef)
      return { success: true }
    } catch (error) {
      console.error("Error deleting experience:", error)
      return { success: false, error: "Failed to delete experience" }
    }
  }

  /**
   * Get day of week from date string (YYYY-MM-DD)
   */
  private static getDayOfWeek(dateString: string): string {
    const date = new Date(dateString)
    const days = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]
    return days[date.getDay()]
  }

  /**
   * Get experience availability for a specific date
   * Precedence: specific date > weekly (day of week) > default
   */
  static async getExperienceAvailability(
    experienceId: string,
    date: string
  ): Promise<ServiceResponse<ExperienceAvailability | null>> {
    try {
      // 1. First, try to get date-specific availability (highest precedence)
      const dateAvailabilityRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.AVAILABILITY_SUBCOLLECTION,
        date
      )
      const dateAvailabilitySnap = await getDoc(dateAvailabilityRef)

      if (dateAvailabilitySnap.exists()) {
        const availability = dateAvailabilitySnap.data() as ExperienceAvailability
        return { success: true, data: { ...availability, type: "specific" } }
      }

      // 2. Try to get weekly availability (day of week)
      const dayOfWeek = this.getDayOfWeek(date)
      const weeklyAvailabilityRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.AVAILABILITY_SUBCOLLECTION,
        dayOfWeek
      )
      const weeklyAvailabilitySnap = await getDoc(weeklyAvailabilityRef)

      if (weeklyAvailabilitySnap.exists()) {
        const weeklyAvailability = weeklyAvailabilitySnap.data() as ExperienceAvailability
        // Create a copy with the requested date but using weekly time slots
        const availability: ExperienceAvailability = {
          ...weeklyAvailability,
          date: date,
          type: "weekly",
        }
        return { success: true, data: availability }
      }

      // 3. Fall back to default availability (lowest precedence)
      const defaultAvailabilityRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.AVAILABILITY_SUBCOLLECTION,
        "default"
      )
      const defaultAvailabilitySnap = await getDoc(defaultAvailabilityRef)

      if (!defaultAvailabilitySnap.exists()) {
        return { success: true, data: null }
      }

      const defaultAvailability = defaultAvailabilitySnap.data() as ExperienceAvailability

      // Create a copy with the requested date but using default time slots
      const availability: ExperienceAvailability = {
        ...defaultAvailability,
        date: date,
        type: "default",
        isDefault: false,
      }

      return { success: true, data: availability }
    } catch (error) {
      console.error("Error getting experience availability:", error)
      return { success: false, error: "Failed to get availability" }
    }
  }

  /**
   * Update experience availability
   */
  static async updateExperienceAvailability(
    experienceId: string,
    availability: ExperienceAvailability
  ): Promise<ServiceResponse<void>> {
    try {
      const availabilityRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.AVAILABILITY_SUBCOLLECTION,
        availability.date
      )
      await setDoc(availabilityRef, availability)
      return { success: true }
    } catch (error) {
      console.error("Error updating experience availability:", error)
      return { success: false, error: "Failed to update availability" }
    }
  }

  /**
   * Create weekly availability patterns for an experience
   */
  static async createWeeklyAvailability(
    experienceId: string,
    daysOfWeek: string[],
    timeSlots: ExperienceAvailability["timeSlots"]
  ): Promise<ServiceResponse<void>> {
    try {
      const batch = writeBatch(db)

      for (const dayOfWeek of daysOfWeek) {
        const availabilityRef = doc(
          db,
          this.COLLECTION,
          experienceId,
          this.AVAILABILITY_SUBCOLLECTION,
          dayOfWeek
        )

        const weeklyAvailability: ExperienceAvailability = {
          date: dayOfWeek,
          type: "weekly",
          daysOfWeek: [dayOfWeek as any],
          timeSlots: timeSlots,
          isDefault: false,
        }

        batch.set(availabilityRef, weeklyAvailability)
      }

      await batch.commit()
      return { success: true }
    } catch (error) {
      console.error("Error creating weekly availability:", error)
      return { success: false, error: "Failed to create weekly availability" }
    }
  }

  /**
   * Check if a time slot is available for booking
   * This checks against confirmed bookings for the specific date and time
   * Uses the experience's booking model to determine availability logic
   */
  static async checkTimeSlotAvailability(
    experienceId: string,
    date: string,
    availabilityId: string,
    requestedGuests: number
  ): Promise<
    ServiceResponse<{
      available: boolean
      maxGuests: number
      currentBookings: number
      remainingSpots: number
    }>
  > {
    try {
      // Get the experience details to check booking model
      const experienceResponse = await this.getExperience(experienceId)
      if (!experienceResponse.success || !experienceResponse.data) {
        return { success: false, error: "Experience not found" }
      }
      const experience = experienceResponse.data

      // Get the availability data for this date
      const availabilityResponse = await this.getExperienceAvailability(experienceId, date)

      if (!availabilityResponse.success || !availabilityResponse.data) {
        return { success: false, error: "No availability data found" }
      }

      const availability = availabilityResponse.data
      const timeSlot = availability.timeSlots.find((slot) => slot.availabilityId === availabilityId)

      if (!timeSlot) {
        return { success: false, error: "Time slot not found" }
      }

      // Check confirmed bookings for this date and time
      const { LocalExperiencesBookingService } = await import("./local-experiences-booking.service")
      const bookingsResponse = await LocalExperiencesBookingService.getExperienceBookings(
        experienceId,
        ["confirmed"], // Only check confirmed bookings
        100 // Get enough to check all bookings for this experience
      )

      if (!bookingsResponse.success) {
        console.error("Failed to check existing bookings:", bookingsResponse.error)
        // If we can't check bookings, assume no bookings exist (safer for new experiences)
        // This allows availability checking to work even when bookings subcollection doesn't exist
      }

      // Count confirmed bookings for this specific date and time
      // Use empty array if bookings response failed or has no data
      const confirmedBookingsForSlot =
        bookingsResponse.success && bookingsResponse.data
          ? bookingsResponse.data.filter(
              (booking) => booking.date === date && booking.availabilityId === availabilityId
            )
          : []

      const totalConfirmedGuests = confirmedBookingsForSlot.reduce(
        (sum, booking) => sum + booking.guests,
        0
      )

      let isAvailable: boolean

      // Default to per_max_guest if bookingModel is not set (backward compatibility)
      const bookingModel = experience.bookingModel || "per_max_guest"

      if (bookingModel === "per_session") {
        // Per session: one booking = one session = host unavailable
        const hasExistingBooking = confirmedBookingsForSlot.length > 0
        isAvailable = !hasExistingBooking && timeSlot.available
      } else {
        // Per max guest: multiple bookings allowed until guest capacity reached
        const availableSpots = timeSlot.maxGuests - totalConfirmedGuests
        const canAccommodateRequest = availableSpots >= requestedGuests
        isAvailable = canAccommodateRequest && timeSlot.available
      }

      return {
        success: true,
        data: {
          available: isAvailable,
          maxGuests: timeSlot.maxGuests,
          currentBookings: totalConfirmedGuests,
          remainingSpots:
            experience.bookingModel === "per_session"
              ? isAvailable
                ? 1
                : 0
              : Math.max(0, timeSlot.maxGuests - totalConfirmedGuests),
        },
      }
    } catch (error) {
      console.error("Error checking time slot availability:", error)
      return { success: false, error: "Failed to check availability" }
    }
  }
}
