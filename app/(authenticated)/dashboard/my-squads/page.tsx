"use client"

import { Suspense } from "react"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { useDashboardContext } from "../components/DashboardProvider"
import { MySquadsContent } from "./components/MySquadsContent"

// Loading skeleton component
const MySquadsSkeleton = () => (
  <div className="space-y-4">
    <div className="h-6 w-32 bg-muted animate-pulse rounded" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-64 bg-muted animate-pulse rounded-lg" />
      ))}
    </div>
  </div>
)

export default function MySquadsPage() {
  const { initialSquads, initialUpcomingTrips } = useDashboardContext()

  // Get real-time updates (these will start with server data and then update)
  const { squads, loading: squadsLoading } = useRealtimeUserSquads(initialSquads)
  const { upcomingTrips, loading: tripsLoading } = useRealtimeUserAllTrips(
    initialUpcomingTrips,
    [] // No past trips needed for this page
  )

  // Use server data immediately, no loading state needed for initial render
  const displaySquads = squads.length > 0 ? squads : initialSquads
  const displayUpcomingTrips = upcomingTrips.length > 0 ? upcomingTrips : initialUpcomingTrips

  return (
    <Suspense fallback={<MySquadsSkeleton />}>
      <MySquadsContent
        squads={displaySquads}
        upcomingTrips={displayUpcomingTrips}
        loading={squadsLoading && displaySquads.length === 0}
      />
    </Suspense>
  )
}
